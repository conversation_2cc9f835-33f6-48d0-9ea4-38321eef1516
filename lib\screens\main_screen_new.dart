import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/bluetooth_service_new.dart';

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<BluetoothService>().initialize();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Bluetooth Alarm Monitor'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        centerTitle: true,
        actions: [
          IconButton(
            onPressed: () => _showDbmRangeDialog(context),
            icon: const Icon(Icons.tune),
            tooltip: 'Set dBm Range',
          ),
        ],
      ),
      body: Consumer<BluetoothService>(
        builder: (context, bluetoothService, child) {
          return Column(
            children: [
              // Header with status
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                margin: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.blue.shade200),
                ),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Icon(
                          Icons.bluetooth_connected,
                          size: 48,
                          color: Colors.blue,
                        ),
                        Text(
                          '${bluetoothService.pairedDevices.length} Paired Devices',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.blue,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      bluetoothService.statusMessage,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Monitor paired devices • Set alarms for disconnection',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ],
                ),
              ),

              // Paired devices section (main focus)
              Expanded(
                flex: 3,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Row(
                        children: [
                          Icon(Icons.bluetooth_connected,
                              color: Colors.blue.shade600),
                          const SizedBox(width: 8),
                          Text(
                            'Paired Devices (${bluetoothService.pairedDevices.length})',
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 8),
                    Expanded(
                      child: bluetoothService.pairedDevices.isEmpty
                          ? Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.bluetooth_disabled,
                                    size: 64,
                                    color: Colors.grey.shade400,
                                  ),
                                  const SizedBox(height: 16),
                                  Text(
                                    bluetoothService.isInitialized
                                        ? 'No paired devices found\nPair devices in Android Bluetooth settings'
                                        : 'Loading paired devices...',
                                    textAlign: TextAlign.center,
                                    style: TextStyle(
                                      fontSize: 16,
                                      color: Colors.grey.shade600,
                                    ),
                                  ),
                                ],
                              ),
                            )
                          : ListView.builder(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 16),
                              itemCount: bluetoothService.pairedDevices.length,
                              itemBuilder: (context, index) {
                                final device =
                                    bluetoothService.pairedDevices[index];
                                return _buildPairedDeviceCard(
                                    device, bluetoothService);
                              },
                            ),
                    ),
                  ],
                ),
              ),

              // Scanned devices section (secondary)
              Expanded(
                flex: 2,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Row(
                        children: [
                          Icon(Icons.radar, color: Colors.green.shade600),
                          const SizedBox(width: 8),
                          Text(
                            'Nearby Devices (${bluetoothService.devices.length})',
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const Spacer(),
                          if (bluetoothService.isScanning)
                            const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                          else
                            ElevatedButton.icon(
                              onPressed: () => bluetoothService.startScan(),
                              icon: const Icon(Icons.search, size: 16),
                              label: const Text('Scan',
                                  style: TextStyle(fontSize: 12)),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.green,
                                foregroundColor: Colors.white,
                                minimumSize: const Size(60, 32),
                              ),
                            ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 8),
                    Expanded(
                      child: bluetoothService.devices.isEmpty
                          ? Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    Icons.bluetooth_searching,
                                    size: 32,
                                    color: Colors.grey.shade400,
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    bluetoothService.isInitialized
                                        ? 'No devices found\nTap "Scan" to search'
                                        : 'Initializing...',
                                    textAlign: TextAlign.center,
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Colors.grey.shade600,
                                    ),
                                  ),
                                ],
                              ),
                            )
                          : ListView.builder(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 16),
                              itemCount: bluetoothService.devices.length,
                              itemBuilder: (context, index) {
                                final device = bluetoothService.devices[index];
                                return _buildScannedDeviceCard(
                                    device, bluetoothService);
                              },
                            ),
                    ),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  // Build card for paired devices (main focus)
  Widget _buildPairedDeviceCard(
      BluetoothDeviceModel device, BluetoothService service) {
    final isConnected = service.isPairedDeviceConnected(device.address);

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 3,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: device.hasAlarm
                ? (isConnected ? Colors.green : Colors.red)
                : Colors.grey.shade300,
            width: 2,
          ),
        ),
        child: Column(
          children: [
            Row(
              children: [
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: isConnected
                        ? Colors.green.shade100
                        : Colors.red.shade100,
                    borderRadius: BorderRadius.circular(30),
                  ),
                  child: Icon(
                    _getDeviceIcon(device.name),
                    color: isConnected ? Colors.green : Colors.red,
                    size: 32,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        device.name,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 18,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        device.address,
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontSize: 12,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: isConnected ? Colors.green : Colors.red,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              isConnected ? 'Connected' : 'Disconnected',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: Colors.blue,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: const Text(
                              'Paired',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: Text(
                    'Disconnect Alarm',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: Colors.grey.shade700,
                    ),
                  ),
                ),
                Switch(
                  value: device.hasAlarm,
                  onChanged: (value) => service.toggleAlarm(device.address),
                  activeColor: Colors.orange,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // Build card for scanned devices (secondary)
  Widget _buildScannedDeviceCard(
      BluetoothDeviceModel device, BluetoothService service) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      elevation: 1,
      child: ListTile(
        dense: true,
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color:
                device.isSaved ? Colors.green.shade100 : Colors.grey.shade100,
            borderRadius: BorderRadius.circular(20),
          ),
          child: Icon(
            _getDeviceIcon(device.name),
            color: device.isSaved ? Colors.green : Colors.grey,
            size: 20,
          ),
        ),
        title: Text(
          device.name,
          style: const TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 14,
          ),
        ),
        subtitle: Row(
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: _getRssiColor(device.rssi),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                '${device.rssi} dBm',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 9,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            if (device.isSaved) ...[
              const SizedBox(width: 6),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.green,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Text(
                  'Saved',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 9,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ],
        ),
        trailing: device.isSaved
            ? Icon(Icons.bookmark, color: Colors.green.shade600, size: 20)
            : ElevatedButton(
                onPressed: () =>
                    service.saveDevice(device.address, device.name),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                  minimumSize: const Size(50, 28),
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                ),
                child: const Text('Save', style: TextStyle(fontSize: 11)),
              ),
      ),
    );
  }

  Color _getRssiColor(int rssi) {
    if (rssi >= -50) return Colors.green;
    if (rssi >= -70) return Colors.orange;
    return Colors.red;
  }

  IconData _getDeviceIcon(String deviceName) {
    final name = deviceName.toLowerCase();
    if (name.contains('airpods') ||
        name.contains('buds') ||
        name.contains('headphone')) {
      return Icons.headset;
    } else if (name.contains('speaker')) {
      return Icons.speaker;
    } else if (name.contains('mouse')) {
      return Icons.mouse;
    } else if (name.contains('keyboard')) {
      return Icons.keyboard;
    } else if (name.contains('phone')) {
      return Icons.phone_android;
    } else if (name.contains('watch')) {
      return Icons.watch;
    } else {
      return Icons.bluetooth;
    }
  }

  void _showDbmRangeDialog(BuildContext context) {
    final service = context.read<BluetoothService>();
    int minDbm = service.minDbm;
    int maxDbm = service.maxDbm;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('Set dBm Range'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('Filter devices by signal strength'),
              const SizedBox(height: 16),
              Text('Minimum dBm: $minDbm'),
              Slider(
                value: minDbm.toDouble(),
                min: -100,
                max: -30,
                divisions: 70,
                onChanged: (value) {
                  setState(() {
                    minDbm = value.round();
                    if (minDbm >= maxDbm) {
                      maxDbm = minDbm + 10;
                    }
                  });
                },
              ),
              Text('Maximum dBm: $maxDbm'),
              Slider(
                value: maxDbm.toDouble(),
                min: -100,
                max: -30,
                divisions: 70,
                onChanged: (value) {
                  setState(() {
                    maxDbm = value.round();
                    if (maxDbm <= minDbm) {
                      minDbm = maxDbm - 10;
                    }
                  });
                },
              ),
              const SizedBox(height: 8),
              Text(
                'Range: $minDbm to $maxDbm dBm',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.blue.shade700,
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                service.setDbmRange(minDbm, maxDbm);
                Navigator.pop(context);
              },
              child: const Text('Apply'),
            ),
          ],
        ),
      ),
    );
  }
}
